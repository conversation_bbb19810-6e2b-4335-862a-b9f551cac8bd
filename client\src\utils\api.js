import axios from 'axios';

// Get API URL from environment variables
const getApiUrl = () => {
  const apiUrl = process.env.REACT_APP_API_URL;
  const nodeEnv = process.env.NODE_ENV;

  console.log(`🌍 Client Environment: ${nodeEnv}`);
  console.log(`🔗 API URL: ${apiUrl}`);

  // Default to localhost for development if no API URL is set
  return apiUrl || 'http://localhost:5000';
};

const api = axios.create({
  baseURL: getApiUrl(),
  timeout: 10000,
  withCredentials: true, // Important for cookies
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export default api; 