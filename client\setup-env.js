#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

const envContent = `# API Configuration - Development
REACT_APP_API_URL=http://localhost:5000

# Environment
NODE_ENV=development

# App Configuration
REACT_APP_APP_NAME=EmpowerPWD
REACT_APP_VERSION=1.0.0
`;

const envPath = path.join(__dirname, '.env.local');

try {
  if (!fs.existsSync(envPath)) {
    fs.writeFileSync(envPath, envContent);
    console.log('✅ Created .env.local file with localhost configuration');
    console.log('📁 File location:', envPath);
    console.log('🔗 API URL set to: http://localhost:5000');
  } else {
    console.log('⚠️  .env.local file already exists');
    console.log('📁 File location:', envPath);
    console.log('🔍 Please check if REACT_APP_API_URL is set to http://localhost:5000');
  }
} catch (error) {
  console.error('❌ Error creating .env.local file:', error.message);
  console.log('\n📝 Please manually create .env.local file with the following content:');
  console.log('\n' + envContent);
}

console.log('\n🚀 Next steps:');
console.log('1. Make sure your backend server is running on port 5000');
console.log('2. Run: npm start');
console.log('3. Your app will connect to http://localhost:5000');


