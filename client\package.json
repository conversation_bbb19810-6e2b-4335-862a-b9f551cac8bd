{"name": "EmpowerPwd", "version": "1.0.0", "private": true, "dependencies": {"@daily-co/daily-js": "^0.73.0", "@fortawesome/fontawesome-free": "^6.6.0", "@headlessui/react": "^2.2.0", "@heroicons/react": "^2.1.5", "@react-oauth/google": "^0.12.1", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@tinymce/tinymce-react": "^5.1.1", "@u-wave/react-vimeo": "^0.9.11", "@videosdk.live/react-sdk": "^0.1.103", "@vonage/server-sdk": "^3.19.2", "axios": "^1.7.7", "bcryptjs": "^2.4.3", "chart.js": "^4.4.7", "class-variance-authority": "^0.7.0", "cloudinary": "^1.21.0", "clsx": "^2.1.1", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.21.1", "framer-motion": "^11.11.11", "jsonwebtoken": "^9.0.2", "jspdf": "^2.5.2", "jspdf-autotable": "^3.8.4", "lucide-react": "^0.453.0", "multer": "^1.4.5-lts.1", "react": "^18.3.1", "react-chartjs-2": "^5.2.0", "react-datepicker": "^7.5.0", "react-dom": "^18.3.1", "react-helmet": "^6.1.0", "react-hook-form": "^7.53.0", "react-hot-toast": "^2.4.1", "react-player": "^2.16.0", "react-quill": "^2.0.0", "react-router-dom": "^6.27.0", "react-scripts": "^5.0.1", "react-toastify": "^10.0.6", "recharts": "^2.13.3", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "styled-components": "^6.1.13", "tailwind-merge": "^2.5.4", "web-vitals": "^2.1.4"}, "devDependencies": {"autoprefixer": "^10.4.20", "concurrently": "^6.0.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.14"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "dev": "concurrently \"npm run server:dev\" \"npm run start:dev\"", "start:dev": "REACT_APP_NODE_ENV=development react-scripts start", "start:prod": "REACT_APP_NODE_ENV=production react-scripts start", "build:dev": "REACT_APP_NODE_ENV=development react-scripts build", "build:prod": "REACT_APP_NODE_ENV=production react-scripts build", "server:dev": "cd ../server && npm run dev", "server:prod": "cd ../server && npm run prod", "dev:win": "concurrently \"npm run server:dev:win\" \"npm run start:dev:win\"", "start:dev:win": "set REACT_APP_NODE_ENV=development && react-scripts start", "start:prod:win": "set REACT_APP_NODE_ENV=production && react-scripts start", "server:dev:win": "cd ../server && npm run dev:win", "server:prod:win": "cd ../server && npm run prod:win"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devServer": {"allowedHosts": "all"}}