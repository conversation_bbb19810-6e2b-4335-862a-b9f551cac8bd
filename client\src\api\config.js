// API Configuration
const API_CONFIG = {
  // Development - Local
  development: {
    baseURL: 'http://localhost:5000',
    apiPath: '/api'
  },
  // Production - Update this when deploying
  production: {
    baseURL: process.env.REACT_APP_API_URL || 'https://your-production-domain.com',
    apiPath: '/api'
  }
};

// Get current environment
const environment = process.env.NODE_ENV || 'development';

// Export the configuration for the current environment
export const config = API_CONFIG[environment];

// Full API URL
export const API_BASE_URL = `${config.baseURL}${config.apiPath}`;

// Individual service URLs
export const AUTH_API = `${config.baseURL}/api/auth`;
export const USER_API = `${config.baseURL}/api/users`;
export const JOB_API = `${config.baseURL}/api/jobs`;
export const EMPLOYER_API = `${config.baseURL}/api/employers`;
export const SEEKER_API = `${config.baseURL}/api/seekers`;
export const ADMIN_API = `${config.baseURL}/api/admin`;
export const UPLOAD_API = `${config.baseURL}/api/upload`;
export const MESSAGE_API = `${config.baseURL}/api/messages`;
export const INTERVIEW_API = `${config.baseURL}/api/interviews`;
export const BLOG_API = `${config.baseURL}/api/blogs`;
export const VIDEO_API = `${config.baseURL}/api/video`;

export default config; 