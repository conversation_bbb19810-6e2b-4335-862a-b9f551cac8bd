import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment-specific configuration
const loadEnvironmentConfig = () => {
  const nodeEnv = process.env.NODE_ENV || 'development';
  
  // Load base .env file first
  dotenv.config({ path: path.join(__dirname, '../.env') });
  
  // Load environment-specific .env file if it exists
  const envFile = path.join(__dirname, `../.env.${nodeEnv}`);
  dotenv.config({ path: envFile });
  
  console.log(`🌍 Environment: ${nodeEnv}`);
  console.log(`📁 Config file: .env.${nodeEnv}`);
};

// Environment configuration object
export const config = {
  // Environment
  NODE_ENV: process.env.NODE_ENV || 'development',
  
  // Server
  PORT: parseInt(process.env.PORT) || 5000,
  APP_URL: process.env.APP_URL || 'http://localhost:5000',
  
  // Database
  MONGODB_URI: process.env.MONGODB_URI || 'mongodb://localhost:27017/empowerpwd',
  
  // Frontend
  FRONTEND_URL: process.env.FRONTEND_URL || 'http://localhost:3000',
  
  // JWT
  JWT_SECRET: process.env.JWT_SECRET || 'fallback_jwt_secret',
  
  // File Upload
  UPLOAD_PATH: process.env.UPLOAD_PATH || 'uploads',
  
  // Email
  EMAIL_USER: process.env.EMAIL_USER,
  EMAIL_APP_PASSWORD: process.env.EMAIL_APP_PASSWORD,
  
  // Video Services
  VIMEO_CLIENT_ID: process.env.VIMEO_CLIENT_ID,
  VIMEO_CLIENT_SECRET: process.env.VIMEO_CLIENT_SECRET,
  VIMEO_ACCESS_TOKEN: process.env.VIMEO_ACCESS_TOKEN,
  VIDEOSDK_API_KEY: process.env.VIDEOSDK_API_KEY,
  VIDEOSDK_SECRET_KEY: process.env.VIDEOSDK_SECRET_KEY,
  DAILY_API_KEY: process.env.DAILY_API_KEY,
  
  // SMS Services
  INFOBIP_API_KEY: process.env.INFOBIP_API_KEY,
  VONAGE_API_KEY: process.env.VONAGE_API_KEY,
  VONAGE_API_SECRET: process.env.VONAGE_API_SECRET,
  TWILIO_ACCOUNT_SID: process.env.TWILIO_ACCOUNT_SID,
  TWILIO_AUTH_TOKEN: process.env.TWILIO_AUTH_TOKEN,
  TWILIO_PHONE_NUMBER: process.env.TWILIO_PHONE_NUMBER,
  
  // Helper methods
  isDevelopment: () => (process.env.NODE_ENV || 'development') === 'development',
  isProduction: () => (process.env.NODE_ENV || 'development') === 'production',
  
  // CORS configuration
  getCorsOptions: () => ({
    origin: process.env.FRONTEND_URL || 'http://localhost:3000',
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization'],
    exposedHeaders: ['set-cookie']
  }),
  
  // Cookie configuration
  getCookieOptions: () => ({
    httpOnly: true,
    secure: (process.env.NODE_ENV || 'development') === 'production',
    sameSite: (process.env.NODE_ENV || 'development') === 'production' ? 'none' : 'lax',
    maxAge: 24 * 60 * 60 * 1000, // 24 hours
    domain: (process.env.NODE_ENV || 'development') === 'production' ? '.vercel.app' : undefined
  })
};

// Initialize configuration
loadEnvironmentConfig();

export default config;
