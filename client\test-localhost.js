#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Function to search for API URL patterns in files
function checkApiUrls(directory) {
  const results = [];
  
  function scanDirectory(dir) {
    const files = fs.readdirSync(dir);
    
    for (const file of files) {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory() && !file.startsWith('.') && file !== 'node_modules') {
        scanDirectory(filePath);
      } else if (file.endsWith('.js') || file.endsWith('.jsx')) {
        const content = fs.readFileSync(filePath, 'utf8');
        
        // Check for patterns that might still be using deployed server
        const patterns = [
          /process\.env\.REACT_APP_API_URL(?!\s*\|\|\s*['"]http:\/\/localhost:5000['"])/g,
          /https?:\/\/[^\/]+\.com/g,
          /https?:\/\/[^\/]+\.net/g,
          /https?:\/\/[^\/]+\.org/g
        ];
        
        patterns.forEach((pattern, index) => {
          const matches = content.match(pattern);
          if (matches) {
            results.push({
              file: filePath,
              pattern: index === 0 ? 'Missing localhost fallback' : 'External URL found',
              matches: matches.length
            });
          }
        });
      }
    }
  }
  
  scanDirectory(directory);
  return results;
}

console.log('🔍 Checking for API URLs that might not be pointing to localhost...\n');

const clientDir = path.join(__dirname, 'src');
const results = checkApiUrls(clientDir);

if (results.length === 0) {
  console.log('✅ All API calls appear to be properly configured for localhost!');
} else {
  console.log('⚠️  Found potential issues:');
  results.forEach(result => {
    console.log(`📁 ${result.file}`);
    console.log(`   ${result.pattern}: ${result.matches} instances`);
    console.log('');
  });
}

console.log('\n🚀 To test your local setup:');
console.log('1. Make sure your backend server is running on port 5000');
console.log('2. Run: npm start');
console.log('3. Check the browser console for any API errors');
console.log('4. Verify that requests are going to http://localhost:5000');


