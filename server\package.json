{"name": "server", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node server.js", "dev": "NODE_ENV=development nodemon server.js", "prod": "NODE_ENV=production node server.js", "dev:win": "set NODE_ENV=development && nodemon server.js", "prod:win": "set NODE_ENV=production && node server.js"}, "type": "module", "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@daily-co/daily-js": "^0.73.0", "@vimeo/vimeo": "^3.0.3", "axios": "^1.7.7", "bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "cloudinary": "^1.41.3", "compression": "^1.7.4", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "date-fns": "^4.1.0", "dotenv": "^16.4.5", "express": "^4.21.1", "express-rate-limit": "^7.4.1", "google-auth-library": "^9.15.0", "googleapis": "^144.0.0", "helmet": "^8.0.0", "http-errors": "^2.0.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.7.3", "multer": "^1.4.5-lts.1", "multer-storage-cloudinary": "^4.0.0", "nodemailer": "^6.9.16", "nodemon": "^3.1.7", "otp-generator": "^4.0.1", "pdfkit": "^0.15.1", "sanitize-filename": "^1.6.3", "twilio": "^5.3.6", "uuid": "^11.0.2"}}