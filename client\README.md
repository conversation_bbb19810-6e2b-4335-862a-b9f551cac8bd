# EmpowerPWD Client

This is the React frontend for the EmpowerPWD application.

## Setup Instructions

### 1. Install Dependencies
```bash
npm install
```

### 2. Environment Configuration
Create a `.env.local` file in the client directory with the following content:

```env
# API Configuration - Development
REACT_APP_API_URL=http://localhost:5000

# Environment
NODE_ENV=development

# App Configuration
REACT_APP_APP_NAME=EmpowerPWD
REACT_APP_VERSION=1.0.0
```

**Important**: The API URL is set to `http://localhost:5000` by default for local development. 
If you need to change this, update the `REACT_APP_API_URL` in your `.env.local` file.

### 3. Start Development Server
```bash
npm start
```

The app will run on `http://localhost:3000` and will connect to your backend server on `http://localhost:5000`.

## API Configuration

The application uses a centralized API configuration system:

- **Development**: Automatically uses `http://localhost:5000` if no environment variable is set
- **Production**: Uses `REACT_APP_API_URL` environment variable
- **Fallback**: If neither is available, defaults to localhost

## Available Scripts

- `npm start` - Start development server
- `npm run build` - Build for production
- `npm test` - Run tests
- `npm run eject` - Eject from Create React App (not recommended)

## Project Structure

```
src/
├── api/           # API configuration and services
├── components/    # React components
├── context/       # React context providers
├── hooks/         # Custom React hooks
├── utils/         # Utility functions
└── styles/        # CSS and styling files
```

## API Endpoints

The application connects to the following API endpoints on your backend:

- **Authentication**: `/api/auth/*`
- **Users**: `/api/users/*`
- **Jobs**: `/api/jobs/*`
- **Employers**: `/api/employers/*`
- **Job Seekers**: `/api/seekers/*`
- **Admin**: `/api/admin/*`
- **Uploads**: `/api/upload`
- **Messages**: `/api/messages/*`
- **Interviews**: `/api/interviews/*`
- **Blogs**: `/api/blogs/*`
- **Video**: `/api/video/*`

## Troubleshooting

### API Connection Issues
1. Ensure your backend server is running on port 5000
2. Check that the `.env.local` file exists and contains the correct API URL
3. Verify that CORS is properly configured on your backend
4. Check the browser console for any error messages

### Environment Variables Not Loading
1. Make sure you're using `.env.local` (not just `.env`)
2. Restart your development server after making changes
3. Verify the variable names start with `REACT_APP_`

## Development Notes

- The app automatically falls back to localhost if no environment variables are set
- All API calls are centralized through the `src/api/config.js` file
- Axios interceptors handle authentication tokens automatically
- Error handling includes automatic redirect to login on 401 responses
