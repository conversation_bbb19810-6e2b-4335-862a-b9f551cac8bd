// import express from 'express';
// import { authMiddleware, roleMiddleware } from '../MiddleWare/authMiddlewareControl.js';
// import { getAllJobs } from '../controllers/jobForSeekerController.js';

// const router = express.Router();

// // Middleware to authenticate and authorize job seekers
// router.use(authMiddleware);
// router.use(roleMiddleware(['jobseeker']));

// // Route to get all active jobs
// router.get('/jobs', getAllJobs);

// export default router;
