# Environment Setup Guide

This guide explains how to easily switch between development and production environments for the EmpowerPWD application.

## 🌍 Environment Configuration

The application now supports proper environment switching with separate configurations for development and production.

### Environment Files

#### Server Environment Files:
- `server/.env` - Development configuration (local development)
- `server/.env.production` - Production configuration (deployed)

#### Client Environment Files:
- `client/.env` - Development configuration (local development)
- `client/.env.production` - Production configuration (deployed)

## 🚀 Quick Start

### For Local Development (Recommended)

1. **Install all dependencies:**
   ```bash
   npm run install:all
   ```

2. **Start development servers:**
   ```bash
   # For Unix/Mac/Linux
   npm run dev
   
   # For Windows
   npm run dev:win
   ```

This will start:
- Server on `http://localhost:5000` (development mode)
- Client on `http://localhost:3000` (development mode)
- MongoDB connection to local database

### For Production Testing

1. **Start production servers:**
   ```bash
   # For Unix/Mac/Linux
   npm run prod
   
   # For Windows
   npm run prod:win
   ```

This will start:
- Server with production configuration
- Client with production API endpoints
- MongoDB connection to production database

## 📋 Available Scripts

### Root Level Scripts
- `npm run dev` - Start both client and server in development mode
- `npm run dev:win` - Windows version of development mode
- `npm run prod` - Start both client and server in production mode
- `npm run prod:win` - Windows version of production mode
- `npm run install:all` - Install dependencies for all projects

### Server Scripts (run from server directory)
- `npm run dev` - Start server in development mode
- `npm run prod` - Start server in production mode
- `npm run dev:win` - Windows development mode
- `npm run prod:win` - Windows production mode

### Client Scripts (run from client directory)
- `npm run start:dev` - Start client in development mode
- `npm run start:prod` - Start client in production mode
- `npm run build:dev` - Build client for development
- `npm run build:prod` - Build client for production

## 🔧 Configuration Details

### Development Mode
- **Server:** `http://localhost:5000`
- **Client:** `http://localhost:3000`
- **Database:** Local MongoDB (`mongodb://localhost:27017/empowerpwd`)
- **CORS:** Allows localhost origins
- **Cookies:** Secure=false, SameSite=lax

### Production Mode
- **Server:** `https://empower-pwd.onrender.com`
- **Client:** `https://empwd.vercel.app`
- **Database:** MongoDB Atlas (cloud)
- **CORS:** Allows production origins only
- **Cookies:** Secure=true, SameSite=none

## 🛠️ Environment Variables

### Server Environment Variables
```env
NODE_ENV=development|production
MONGODB_URI=<database_connection_string>
PORT=5000|5001
FRONTEND_URL=http://localhost:3000|https://empwd.vercel.app
JWT_SECRET=<your_jwt_secret>
UPLOAD_PATH=uploads|/opt/render/project/uploads
```

### Client Environment Variables
```env
REACT_APP_API_URL=http://localhost:5000|https://empower-pwd.onrender.com
REACT_APP_NODE_ENV=development|production
REACT_APP_VIDEOSDK_API_KEY=<your_videosdk_key>
```

## 🔄 Switching Environments

The application automatically detects the environment based on the `NODE_ENV` variable:

1. **To switch to development:**
   - Use `npm run dev` or `npm run dev:win`
   - This sets `NODE_ENV=development`

2. **To switch to production:**
   - Use `npm run prod` or `npm run prod:win`
   - This sets `NODE_ENV=production`

## 📝 Important Notes

1. **Database Setup:** Make sure you have MongoDB running locally for development mode
2. **Environment Files:** Never commit sensitive production credentials to version control
3. **CORS:** The application automatically configures CORS based on the environment
4. **Cookies:** Cookie settings are automatically adjusted for each environment
5. **File Uploads:** Upload paths are configured differently for local vs production

## 🐛 Troubleshooting

### Common Issues:

1. **CORS Errors:**
   - Check that `FRONTEND_URL` matches your client URL
   - Ensure you're using the correct environment scripts

2. **Database Connection:**
   - Development: Ensure MongoDB is running locally
   - Production: Check MongoDB Atlas connection string

3. **API Calls Failing:**
   - Verify `REACT_APP_API_URL` is set correctly
   - Check that server is running on the expected port

4. **Cookie Issues:**
   - Development: Cookies work with localhost
   - Production: Requires HTTPS and proper domain configuration

## 🎯 Current Status

✅ **Development Mode:** Configured for local development
- Server: `http://localhost:5000`
- Client: `http://localhost:3000`
- Database: Local MongoDB

⚠️ **Production Mode:** Ready for deployment
- Server: Production URLs configured
- Client: Production API endpoints configured
- Database: MongoDB Atlas configured

Use `npm run dev` to start local development!
