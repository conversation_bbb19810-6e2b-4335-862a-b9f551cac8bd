{"name": "empowerpwd-fullstack", "version": "1.0.0", "description": "EmpowerPWD Full Stack Application", "scripts": {"dev": "concurrently \"npm run server:dev\" \"npm run client:dev\"", "dev:win": "concurrently \"npm run server:dev:win\" \"npm run client:dev:win\"", "prod": "concurrently \"npm run server:prod\" \"npm run client:prod\"", "prod:win": "concurrently \"npm run server:prod:win\" \"npm run client:prod:win\"", "server:dev": "cd server && npm run dev", "server:dev:win": "cd server && npm run dev:win", "server:prod": "cd server && npm run prod", "server:prod:win": "cd server && npm run prod:win", "client:dev": "cd client && npm run start:dev", "client:dev:win": "cd client && npm run start:dev:win", "client:prod": "cd client && npm run start:prod", "client:prod:win": "cd client && npm run start:prod:win", "build:dev": "cd client && npm run build:dev", "build:prod": "cd client && npm run build:prod", "install:all": "npm install && cd server && npm install && cd ../client && npm install", "setup:dev": "npm run install:all && echo Setup complete! Run npm run dev to start development server", "setup:prod": "npm run install:all && echo Setup complete! Run npm run prod to start production server", "start": "node server.js"}, "dependencies": {"@heroicons/react": "^2.1.5", "@react-oauth/google": "^0.12.1", "@u-wave/react-vimeo": "^0.9.11", "@vonage/server-sdk": "^3.19.2", "chart.js": "^4.4.7", "dotenv": "^16.4.5", "framer-motion": "^11.11.11", "jspdf": "^2.5.2", "jspdf-autotable": "^3.8.4", "multer": "^1.4.5-lts.1", "react-helmet": "^6.1.0", "styled-components": "^6.1.13"}, "devDependencies": {"concurrently": "^8.2.2"}, "devServer": {"allowedHosts": "all"}, "keywords": ["empowerpwd", "fullstack", "react", "express", "mongodb"], "author": "EmpowerPWD Team", "license": "MIT"}